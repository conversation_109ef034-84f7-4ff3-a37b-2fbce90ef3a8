import { initializeApp } from 'firebase/app'
import {
  browserSessionPersistence,
  connectAuthEmulator,
  getAuth,
  GoogleAuthProvider,
  onAuthStateChanged,
  setPersistence,
} from 'firebase/auth'
import { connectFirestoreEmulator, getFirestore } from 'firebase/firestore'
import { getMessaging } from 'firebase/messaging'
import { connectStorageEmulator, getStorage } from 'firebase/storage'
import { getGenerativeModel, getVertexAI } from 'firebase/vertexai'

// Retry logic for Firestore operations to handle WebChannelConnection errors
export async function retryFirestoreOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
): Promise<T> {
  let lastError: Error | undefined

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    }
    catch (error: any) {
      lastError = error
      console.warn(`Firestore operation attempt ${attempt} failed:`, error.message)

      // Don't retry on certain error types
      if (
        error.code === 'permission-denied'
        || error.code === 'unauthenticated'
        || error.code === 'invalid-argument'
      ) {
        throw error
      }

      if (attempt < maxRetries) {
        console.warn(`Retrying in ${delay}ms...`)
        await new Promise(resolve => setTimeout(resolve, delay * attempt))
      }
    }
  }

  throw new Error(lastError?.message || 'Firestore operation failed after retries')
}

export function useFirebase() {
  const config = useRuntimeConfig?.() || { public: { firebaseConfig: {} } }
  const isDevelopment = import.meta.env?.DEV || false
  const firebaseConfig: any = config.public.firebaseConfig

  const firebaseApp = initializeApp(firebaseConfig)
  const auth = getAuth(firebaseApp)
  const firestore = getFirestore(firebaseApp)
  const googleProvider = new GoogleAuthProvider()
  let storage: any = null
  let messaging: any = null

  // Enable Firestore offline persistence to handle network issues better
  if (typeof window !== 'undefined') {
    // Note: enablePersistence is deprecated, but keeping for reference
    // Modern Firebase handles offline persistence automatically
    console.warn('[Firebase] Firestore initialized with automatic offline persistence')
  }

  // Configure Google provider
  googleProvider.addScope('profile')
  googleProvider.addScope('email')

  const vertexAI = getVertexAI(firebaseApp)
  const model = getGenerativeModel(vertexAI, {
    model: 'gemini-1.5-flash-preview-0514',
  })

  if (typeof window !== 'undefined') {
    messaging = getMessaging(firebaseApp)
    storage = getStorage(firebaseApp)

    if (isDevelopment) {
      try {
        connectStorageEmulator(storage, 'localhost', 9199)
      }
      catch (error) {
        console.warn('Storage emulator connection failed:', error)
      }
    }
  }

  // Enhanced emulator connection with better error handling
  const host
    = (firestore.toJSON() as { settings?: { host?: string } }).settings?.host
      ?? ''

  if (isDevelopment) {
    if (!host.startsWith('localhost')) {
      try {
        connectFirestoreEmulator(firestore, 'localhost', 8080)
        console.warn('Connected to Firestore emulator')
      }
      catch (error) {
        console.warn('Firestore emulator connection failed:', error)
        console.warn('Continuing with production Firestore...')
      }
    }

    const authUrl = host.startsWith('localhost')
      ? 'http://localhost:9099'
      : 'http://127.0.0.1:9099'
    try {
      connectAuthEmulator(auth, authUrl, { disableWarnings: true })
      console.warn('Connected to Auth emulator')
    }
    catch (error) {
      console.warn('Auth emulator connection failed:', error)
      console.warn('Continuing with production Auth...')
    }
  }

  // Enhanced persistence setup with better error handling
  setPersistence(auth, browserSessionPersistence).catch((err) => {
    console.error('Error enabling auth persistence:', err)
    console.warn('Continuing without persistence...')
  })

  // Network state monitoring to help diagnose WebChannelConnection issues
  if (typeof window !== 'undefined' && 'navigator' in globalThis) {
    const handleOnline = () => {
      console.warn('Network connection restored - Firestore should reconnect')
    }

    const handleOffline = () => {
      console.warn('Network connection lost - this may cause WebChannelConnection errors')
    }

    globalThis.addEventListener('online', handleOnline)
    globalThis.addEventListener('offline', handleOffline)
  }

  // Add Firestore connection monitoring to help debug WebChannelConnection issues
  const monitorFirestoreConnection = () => {
    if (typeof window !== 'undefined') {
      console.warn('Firestore connection monitoring active - watching for WebChannelConnection errors')

      // Monitor for network errors that could cause WebChannelConnection issues
      const handleError = (event: ErrorEvent) => {
        if (event.message?.includes('WebChannelConnection') || event.message?.includes('RPC')) {
          console.error('Detected WebChannelConnection error:', event.message)
          console.warn('Consider checking your network connection or Firebase emulator status')
        }
      }

      window.addEventListener('error', handleError)

      // Return cleanup function
      return () => {
        window.removeEventListener('error', handleError)
      }
    }
    return () => {}
  }

  return {
    firebaseApp,
    firestore,
    auth,
    googleProvider,
    vertexAI,
    model,
    storage,
    onAuthStateChanged,
    messaging,
    monitorFirestoreConnection,
    retryFirestoreOperation,
  }
}

import type { Profile } from '../types/profile'
import type { Workspace } from '../types/workspace'
import {
  createUserWithEmailAndPassword,
  sendPasswordResetEmail,
  signInWithEmailAndPassword,
  signInWithPopup,
  signOut,
} from 'firebase/auth'
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  serverTimestamp,
  setDoc,
  where,
} from 'firebase/firestore'
import { handleFirestoreError } from '../utils/firestore-helpers'
import { nullToUndefined } from '../utils/type-helpers'
import { useFirebase } from './firebase'

interface PublicUser {
  id: string
  email: string
  username: string
  token: any
}

interface AuthState {
  user: PublicUser | null
  currentWorkspace: Workspace | null
  currentProfile: Profile | null
  workspaces: Workspace[]
  isAuthenticated: boolean
}

// Helper function to retry Firestore operations with enhanced error handling
async function retryFirestoreOperation<T>(
  operation: () => Promise<T>,
  operationName: string = 'Firestore operation',
  maxRetries = 3,
  delay = 1000,
): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    }
    catch (error: any) {
      const errorInfo = handleFirestoreError(error, operationName)

      // Don't retry if it's not a network error or shouldn't be retried
      if (!errorInfo.shouldRetry || attempt === maxRetries) {
        console.error(`[Auth] ${operationName} failed permanently:`, errorInfo.userMessage)
        throw error
      }

      console.warn(`[Auth] ${operationName} attempt ${attempt} failed, retrying...`)

      // Wait before retrying, with exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay * attempt))
    }
  }
  throw new Error('Max retries exceeded')
}

export function useAuth() {
  const toaster = useNuiToasts()
  const { $fetch } = useNuxtApp()

  const { auth, firestore } = useFirebase()

  // Helper functions for toasts
  const showError = (message: string) => {
    console.warn('🚨 [Auth] Showing error toast:', message)
    console.warn('🚨 [Auth] Toast config:', {
      title: 'Error',
      message,
      color: 'danger',
      icon: 'heroicons:x-circle',
      closable: true,
      duration: 8000,
    })

    toaster.add({
      title: 'Login Error',
      message,
      color: 'red',
      icon: 'heroicons:x-circle',
      closable: true,
      duration: 10000, // Show for 10 seconds
      ui: {
        background: 'bg-red-50 dark:bg-red-950',
        ring: 'ring-1 ring-red-200 dark:ring-red-800',
        title: 'text-red-900 dark:text-red-100',
        description: 'text-red-700 dark:text-red-300',
      },
    })
  }

  const showSuccess = (message: string) => {
    toaster.add({
      title: 'Success',
      message,
      color: 'success',
      icon: 'heroicons:check-circle',
      closable: true,
      duration: 5000, // Show for 5 seconds
    })
  }

  // Create cookies for persistent state
  const userCookie = useCookie<PublicUser | null>('auth:user')
  const workspacesCookie = useCookie<Workspace[]>('auth:workspaces', {
    default: () => [],
  })
  const currentWorkspaceCookie = useCookie<Workspace | null>('auth:currentWorkspace')
  const currentProfileCookie = useCookie<Profile | null>('auth:currentProfile')

  // Create reactive state with initial values from cookies
  const state = useState<AuthState>('auth', () => ({
    user: userCookie.value || null,
    currentWorkspace: currentWorkspaceCookie.value || null,
    currentProfile: currentProfileCookie.value || null,
    workspaces: workspacesCookie.value || [],
    isAuthenticated: !!userCookie.value,
  }))

  // Setters that update both state and cookies
  const setUser = (user: PublicUser | null) => {
    state.value.user = user
    state.value.isAuthenticated = !!user
    userCookie.value = user
  }

  const setWorkspaces = (workspaces: Workspace[]) => {
    state.value.workspaces = workspaces
    workspacesCookie.value = workspaces
  }

  const setCurrentWorkspace = async (workspaceId: string) => {
    const workspace = state.value.workspaces.find(w => w.id === workspaceId)
    if (!workspace)
      return

    try {
      let profile: Profile
      let profileId: string

      // Check if workspace has a profileId (using existing profile)
      if (workspace.profileId) {
        // Fetch the linked profile
        const profileRef = doc(firestore, 'profiles', workspace.profileId)
        const profileSnap = await getDoc(profileRef)

        if (profileSnap.exists()) {
          profile = profileSnap.data() as Profile
          profileId = profileRef.id
        }
        else {
          throw new Error('Linked profile not found')
        }
      }
      else {
        // Get the profile for this workspace (dedicated profile)
        const profileRef = doc(firestore, 'profiles', `${state.value.user?.id}_${workspaceId}`)
        const profileSnap = await getDoc(profileRef)

        if (profileSnap.exists()) {
          profile = profileSnap.data() as Profile
          profileId = profileRef.id
        }
        else {
          throw new Error('Profile not found')
        }
      }

      // Update state and cookies
      state.value.currentProfile = profile
      state.value.currentProfile.id = profileId
      state.value.currentWorkspace = workspace
      state.value.currentWorkspace.id = workspaceId

      currentProfileCookie.value = profile
      currentWorkspaceCookie.value = workspace
    }
    catch (error) {
      console.error('Failed to fetch workspace profile:', error)
      throw error
    }
  }

  // Firebase Auth Actions
  const login = async (email: string, password: string) => {
    try {
      // Sign in with Firebase Auth
      const userCredential: any = await signInWithEmailAndPassword(auth, email, password)
      const firebaseUser = userCredential.user

      // Get user data from Firestore with retry logic
      const userDoc = await retryFirestoreOperation(() =>
        getDoc(doc(firestore, 'users', firebaseUser.uid)),
      )

      let userData: any
      if (!userDoc.exists()) {
        console.error('[Auth] User document not found in Firestore for uid:', firebaseUser.uid)
        console.log('[Auth] Attempting to create missing user document...')

        // Try to create the missing user document with retry logic
        try {
          const username = firebaseUser.email?.split('@')[0] || 'user'
          await retryFirestoreOperation(() =>
            setDoc(doc(firestore, 'users', firebaseUser.uid), {
              email: firebaseUser.email,
              username,
              createdAt: serverTimestamp(),
              updatedAt: serverTimestamp(),
              isActive: true,
              deletedAt: null,
            }), 'Create user document')

          // Create default workspace with retry logic
          const workspaceRef = doc(collection(firestore, 'workspaces'))
          const workspaceData = {
            name: `${username}'s Workspace`,
            slug: username.toLowerCase(),
            description: undefined,
            logoUrl: null,
            ownerId: firebaseUser.uid,
            createdBy: firebaseUser.uid,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            deletedAt: null,
          }
          await retryFirestoreOperation(() => setDoc(workspaceRef, workspaceData), 'Create workspace')

          // Create workspace membership with retry logic
          const memberRef = doc(firestore, 'workspace_members', `${workspaceRef.id}_${firebaseUser.uid}`)
          await retryFirestoreOperation(() =>
            setDoc(memberRef, {
              workspaceId: workspaceRef.id,
              userId: firebaseUser.uid,
              role: 'owner',
              createdAt: serverTimestamp(),
              updatedAt: serverTimestamp(),
            }), 'Create workspace membership')

          // Create profile with retry logic
          const profileRef = doc(firestore, 'profiles', `${firebaseUser.uid}_${workspaceRef.id}`)
          await retryFirestoreOperation(() =>
            setDoc(profileRef, {
              userId: firebaseUser.uid,
              workspaceId: workspaceRef.id,
              displayName: username,
              bio: null,
              avatarUrl: null,
              createdAt: serverTimestamp(),
              updatedAt: serverTimestamp(),
              deletedAt: null,
            }), 'Create user profile')

          showSuccess('Account has been set up successfully!')

          // Re-fetch the user document with retry logic
          const newUserDoc = await retryFirestoreOperation(() =>
            getDoc(doc(firestore, 'users', firebaseUser.uid)), 'Fetch user document')
          if (!newUserDoc.exists()) {
            throw new Error('Failed to create user data')
          }
          userData = newUserDoc.data()
        }
        catch (createError) {
          console.error('[Auth] Failed to create user data:', createError)
          showError('Failed to set up your account. Please try signing up instead.')
          throw new Error('User data not found in database')
        }
      }
      else {
        userData = userDoc.data()
      }

      // Add a frontend test write after successful login with retry logic
      try {
        await retryFirestoreOperation(() =>
          setDoc(doc(firestore, 'frontend-test-writes', `login_test_${firebaseUser.uid}`), {
            userId: firebaseUser.uid,
            timestamp: new Date().toISOString(),
            message: 'Frontend login test write',
          }), 'Frontend test write')
      }
      catch (testError) {
        console.warn('[Auth] Frontend test write failed, but this is non-critical:', testError)
      }

      const publicUser: PublicUser = {
        id: firebaseUser.uid,
        email: firebaseUser.email!,
        username: userData.username,
        token: userCredential._tokenResponse,
      }

      // Get user's workspaces with retry logic
      const workspaceMembersRef = collection(firestore, 'workspace_members')
      const q = query(workspaceMembersRef, where('userId', '==', firebaseUser.uid))
      const membershipDocs = await retryFirestoreOperation(() => getDocs(q), 'Fetch workspace memberships')

      const workspaces: Workspace[] = []
      for (const memberDoc of membershipDocs.docs) {
        const memberData = memberDoc.data()
        const workspaceDoc = await retryFirestoreOperation(() =>
          getDoc(doc(firestore, 'workspaces', memberData.workspaceId)), 'Fetch workspace details')

        if (workspaceDoc.exists()) {
          const workspaceData = workspaceDoc.data()
          workspaces.push({
            id: workspaceDoc.id,
            name: workspaceData.name,
            slug: workspaceData.slug,
            description: nullToUndefined(workspaceData.description),
            type: workspaceData.type,
            logoUrl: nullToUndefined(workspaceData.logoUrl),
            ownerId: workspaceData.ownerId,
            createdBy: workspaceData.createdBy,
            profileId: workspaceData.profileId,
            role: memberData.role,
            createdAt: workspaceData.createdAt,
            updatedAt: workspaceData.updatedAt,
            deletedAt: nullToUndefined(workspaceData.deletedAt),
          })
        }
      }

      // Update state
      setUser(publicUser)
      setWorkspaces(workspaces)

      // Set current workspace to the first one
      if (workspaces.length > 0) {
        await setCurrentWorkspace(workspaces[0].id)
      }
      await setSessionServer()

      console.log('[Auth] Login completed. User:', publicUser)
      console.log('[Auth] Workspaces:', workspaces)
      console.log('[Auth] Current auth state:', state.value)

      return { user: publicUser, workspaces, currentWorkspaceId: workspaces[0]?.id }
    }
    catch (error: any) {
      console.error('[Auth] Login error:', error)

      // Handle specific Firebase auth errors
      let errorMessage = 'Login failed'

      if (error.code === 'auth/user-not-found') {
        errorMessage = 'No account found with this email address'
      }
      else if (error.code === 'auth/wrong-password') {
        errorMessage = 'Incorrect password'
      }
      else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Invalid email address'
      }
      else if (error.code === 'auth/user-disabled') {
        errorMessage = 'This account has been disabled'
      }
      else if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Too many failed login attempts. Please try again later'
      }
      else if (error.message) {
        errorMessage = error.message
      }

      showError(errorMessage)
      throw error
    }
  }

  const signup = async (formData: {
    email: string
    username: string
    password: string
    confirmPassword: string
  }) => {
    try {
      // Use the client-side Firebase auth to create the user
      const userCredential = await createUserWithEmailAndPassword(auth, formData.email, formData.password)
      const firebaseUser = userCredential.user

      // Create user document in Firestore
      const userRef = doc(firestore, 'users', firebaseUser.uid)
      await setDoc(userRef, {
        email: formData.email,
        username: formData.username,
        displayName: formData.username,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        isActive: true,
        deletedAt: null,
        settings: {
          theme: 'system',
          language: 'en',
          notifications: {
            email: true,
            push: true,
            marketing: false,
          },
          security: {
            twoFactorEnabled: false,
          },
        },
      })

      // Create default workspace
      const workspaceRef = doc(collection(firestore, 'workspaces'))
      const workspaceData = {
        name: `${formData.username}'s Workspace`,
        slug: formData.username.toLowerCase(),
        description: nullToUndefined(null),
        logoUrl: nullToUndefined(null),
        ownerId: firebaseUser.uid,
        createdBy: firebaseUser.uid,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        deletedAt: nullToUndefined(null),
      }
      await setDoc(workspaceRef, workspaceData)

      // Create workspace membership
      const memberRef = doc(firestore, 'workspace_members', `${workspaceRef.id}_${firebaseUser.uid}`)
      await setDoc(memberRef, {
        workspaceId: workspaceRef.id,
        userId: firebaseUser.uid,
        role: 'owner',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      // Create profile
      const profileRef = doc(firestore, 'profiles', `${firebaseUser.uid}_${workspaceRef.id}`)
      await setDoc(profileRef, {
        userId: firebaseUser.uid,
        workspaceId: workspaceRef.id,
        displayName: formData.username,
        bio: nullToUndefined(null),
        avatarUrl: nullToUndefined(null),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        deletedAt: nullToUndefined(null),
      })

      // Update user state
      const publicUser: PublicUser = {
        id: firebaseUser.uid,
        email: formData.email,
        username: formData.username,
        token: (userCredential as any)._tokenResponse,
      }

      const workspace: Workspace = {
        id: workspaceRef.id,
        name: workspaceData.name,
        slug: workspaceData.slug,
        description: workspaceData.description,
        logoUrl: workspaceData.logoUrl,
        ownerId: workspaceData.ownerId,
        createdBy: workspaceData.createdBy,
        createdAt: workspaceData.createdAt,
        updatedAt: workspaceData.updatedAt,
        deletedAt: workspaceData.deletedAt,
        role: 'owner',
      }

      setUser(publicUser)
      setWorkspaces([workspace])
      await setCurrentWorkspace(workspace.id)
      await setSessionServer()

      showSuccess('Account created successfully!')
      return { user: publicUser, workspaceId: workspaceRef.id }
    }
    catch (error: any) {
      console.error('Signup error:', error)
      showError(error.message)
      throw error
    }
  }

  const logout = async () => {
    try {
      await signOut(auth)

      // Clear state and cookies
      state.value = {
        user: null,
        currentWorkspace: null,
        currentProfile: null,
        workspaces: [],
        isAuthenticated: false,
      }

      userCookie.value = null
      workspacesCookie.value = []
      currentWorkspaceCookie.value = null
      currentProfileCookie.value = null
      await setSessionServer()

      showSuccess('Logged out successfully')
    }
    catch (error: any) {
      console.error('Logout error:', error)
      showError(error.message)
      throw error
    }
  }

  const resetPassword = async (email: string) => {
    try {
      await sendPasswordResetEmail(auth, email)
      showSuccess('Password reset email sent')
    }
    catch (error: any) {
      console.error('Password reset error:', error)
      showError(error.message)
      throw error
    }
  }

  const loginWithGoogle = async () => {
    try {
      const { googleProvider } = useFirebase()
      const result = await signInWithPopup(auth, googleProvider)
      const firebaseUser = result.user

      // Check if user exists in our database
      const userRef = doc(firestore, 'users', firebaseUser.uid)
      const userDoc = await getDoc(userRef)

      if (!userDoc.exists()) {
        // New Google user - create user record and workspace
        const username = firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User'

        // Create user document
        await setDoc(userRef, {
          email: firebaseUser.email,
          username,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          isActive: true,
          deletedAt: null,
        })

        // Create default workspace
        const workspaceRef = doc(collection(firestore, 'workspaces'))
        const workspaceData = {
          name: `${username}'s Workspace`,
          slug: username.toLowerCase().replace(/\s+/g, '-'),
          description: undefined,
          logoUrl: nullToUndefined(null),
          ownerId: firebaseUser.uid,
          createdBy: firebaseUser.uid,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          deletedAt: nullToUndefined(null),
        }
        await setDoc(workspaceRef, workspaceData)

        // Create workspace membership
        const memberRef = doc(firestore, 'workspace_members', `${workspaceRef.id}_${firebaseUser.uid}`)
        await setDoc(memberRef, {
          workspaceId: workspaceRef.id,
          userId: firebaseUser.uid,
          role: 'owner',
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })

        // Create profile
        const profileRef = doc(firestore, 'profiles', `${firebaseUser.uid}_${workspaceRef.id}`)
        await setDoc(profileRef, {
          userId: firebaseUser.uid,
          workspaceId: workspaceRef.id,
          displayName: username,
          bio: nullToUndefined(null),
          avatarUrl: nullToUndefined(firebaseUser.photoURL),
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          deletedAt: nullToUndefined(null),
        })

        // Set up new user state
        const publicUser: PublicUser = {
          id: firebaseUser.uid,
          email: firebaseUser.email!,
          username,
          token: (result as any)._tokenResponse,
        }

        const workspace: Workspace = {
          id: workspaceRef.id,
          name: workspaceData.name,
          slug: workspaceData.slug,
          description: workspaceData.description,
          logoUrl: workspaceData.logoUrl,
          ownerId: workspaceData.ownerId,
          createdBy: workspaceData.createdBy,
          createdAt: workspaceData.createdAt,
          updatedAt: workspaceData.updatedAt,
          deletedAt: workspaceData.deletedAt,
          role: 'owner',
        }

        setUser(publicUser)
        setWorkspaces([workspace])
        await setCurrentWorkspace(workspace.id)
      }
      else {
        // Existing user - load their data
        const userData = userDoc.data()
        const publicUser: PublicUser = {
          id: firebaseUser.uid,
          email: firebaseUser.email!,
          username: userData.username,
          token: (result as any)._tokenResponse,
        }

        setUser(publicUser)
        await loadUserWorkspaces()
      }

      await setSessionServer()
      showSuccess('Successfully logged in with Google!')

      return { success: true }
    }
    catch (error: any) {
      console.error('Google login error:', error)
      showError(error.message || 'Failed to login with Google')
      throw error
    }
  }

  const switchWorkspace = async (workspaceId: string) => {
    try {
      await setCurrentWorkspace(workspaceId)
      await setSessionServer()
      showSuccess('Workspace switched successfully')
    }
    catch (error: any) {
      console.error('Switch workspace error:', error)
      showError(error.message)
      throw error
    }
  }

  const loadUserWorkspaces = async () => {
    if (!state.value.user)
      return

    try {
      const workspaceMembersRef = collection(firestore, 'workspace_members')
      const q = query(workspaceMembersRef, where('userId', '==', state.value.user.id))
      const membershipDocs = await getDocs(q)

      const workspaces: Workspace[] = []
      for (const memberDoc of membershipDocs.docs) {
        const memberData = memberDoc.data()
        const workspaceDoc = await getDoc(doc(firestore, 'workspaces', memberData.workspaceId))

        if (workspaceDoc.exists()) {
          const workspaceData = workspaceDoc.data()
          workspaces.push({
            id: workspaceDoc.id,
            name: workspaceData.name,
            slug: workspaceData.slug,
            description: nullToUndefined(workspaceData.description),
            type: workspaceData.type,
            logoUrl: nullToUndefined(workspaceData.logoUrl),
            ownerId: workspaceData.ownerId,
            createdBy: workspaceData.createdBy,
            profileId: workspaceData.profileId,
            role: memberData.role,
            createdAt: workspaceData.createdAt,
            updatedAt: workspaceData.updatedAt,
            deletedAt: nullToUndefined(workspaceData.deletedAt),
          })
        }
      }

      setWorkspaces(workspaces)

      // Set current workspace to the first one if none selected
      if (workspaces.length > 0 && !state.value.currentWorkspace) {
        await setCurrentWorkspace(workspaces[0].id!)
      }
    }
    catch (error) {
      console.error('Failed to load user workspaces:', error)
      showError('Failed to load workspaces')
    }
  }

  async function setSessionServer() {
    try {
      const stateResponse = await $fetch('/api/auth/set', {
        method: 'POST',
        body: state.value,
      })
      state.value = stateResponse as AuthState
    }
    catch (error) {
      console.error('Failed to set session:', error)
      showError('Failed to set session')
    }
  }

  // Getters
  const getCurrentWorkspaceId = computed(() => state.value.currentWorkspace?.id)
  const getCurrentProfileId = computed(() => state.value.currentProfile?.id)
  const getCurrentWorkspaceRole = computed(() => state.value.currentWorkspace?.role)
  const isWorkspaceOwner = computed(() => state.value.currentWorkspace?.role === 'owner')
  const isWorkspaceAdmin = computed(() =>
    ['owner', 'admin'].includes(state.value.currentWorkspace?.role || ''),
  )

  const accountTypes: any = useState('accountTypes', () => {
    return [
      {
        value: 'individual',
        name: 'Individual',
        image: '/img/illustrations/wizard/design.svg',
        description: 'Create a personal account for yourself',
      },
      {
        value: 'family',
        name: 'Family',
        image: '/img/illustrations/wizard/development.svg',
        description: 'Create a family account for your family members',
      },
      {
        value: 'group',
        name: 'Group',
        image: '/img/illustrations/wizard/marketing.svg',
        description: 'Create a group account for your team members',
      },
      {
        value: 'company',
        name: 'Company',
        image: '/img/illustrations/wizard/team.svg',
        description: 'Create a company account',
      },
    ]
  })

  const accountTools = useState('accountTools', () => {
    return [
      {
        name: 'Illustrator',
        description: 'Design Software',
        image: '/img/logos/tools/illustrator.svg',
      },
      {
        name: 'Photoshop',
        description: 'Design Software',
        image: '/img/logos/tools/photoshop.svg',
      },
      {
        name: 'Adobe XD',
        description: 'Design Software',
        image: '/img/logos/tools/xd.svg',
      },
      {
        name: 'Figma',
        description: 'Design Software',
        image: '/img/logos/tools/xd.svg',
      },
      {
        name: 'Invision',
        description: 'Design Software',
        image: '/img/logos/tools/invision.svg',
      },
      {
        name: 'Jira',
        description: 'Issue Tracker',
        image: '/img/logos/tools/jira.svg',
      },
      {
        name: 'Taiga',
        description: 'Scrumboard',
        image: '/img/logos/tools/taiga.svg',
      },
      {
        name: 'Slack',
        description: 'Messaging App',
        image: '/img/logos/tools/slack.svg',
      },
      {
        name: 'Asana',
        description: 'Task Manager',
        image: '/img/logos/tools/asana.svg',
      },
      {
        name: 'Teamwork',
        description: 'Collaborative App',
        image: '/img/logos/tools/teamwork.svg',
      },
      {
        name: 'GitHub',
        description: 'Code Repository',
        image: '/img/logos/tools/github.svg',
      },
      {
        name: 'Gitlab',
        description: 'Code Repository',
        image: '/img/logos/tools/gitlab.svg',
      },
    ]
  })

  const makeUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0; const v = c == 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  const getUserProfiles = async () => {
    if (!state.value.user)
      return []

    try {
      const profiles: Array<Profile & { workspaceName: string }> = []

      // Get all profiles for the user
      const profilesRef = collection(firestore, 'profiles')
      const q = query(profilesRef, where('userId', '==', state.value.user.id))
      const profileDocs = await getDocs(q)

      for (const profileDoc of profileDocs.docs) {
        const profileData = profileDoc.data() as Profile

        // Get workspace name for this profile
        const workspaceDoc = await getDoc(doc(firestore, 'workspaces', profileData.workspaceId))
        if (workspaceDoc.exists()) {
          const workspaceData = workspaceDoc.data()
          profiles.push({
            ...profileData,
            id: profileDoc.id,
            workspaceName: workspaceData.name,
          })
        }
      }

      return profiles
    }
    catch (error) {
      console.error('Failed to get user profiles:', error)
      return []
    }
  }

  const createWorkspace = async (workspaceData: {
    name: string
    description?: string
    type?: string
    useExistingProfile?: boolean
    existingProfileId?: string
    newProfileData?: {
      displayName: string
      bio?: string
    }
  }) => {
    try {
      if (!state.value.user) {
        throw new Error('User must be logged in to create a workspace')
      }

      const userId = state.value.user.id

      // Create workspace document
      const workspaceRef = doc(collection(firestore, 'workspaces'))
      const workspaceDoc = {
        name: workspaceData.name,
        slug: workspaceData.name.toLowerCase().replace(/\s+/g, '-'),
        description: workspaceData.description || null,
        type: workspaceData.type || 'personal',
        logoUrl: null,
        ownerId: userId,
        createdBy: userId,
        profileId: workspaceData.useExistingProfile ? workspaceData.existingProfileId : null,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        deletedAt: null,
      }
      await setDoc(workspaceRef, workspaceDoc)

      // Create workspace membership
      const memberRef = doc(firestore, 'workspace_members', `${workspaceRef.id}_${userId}`)
      await setDoc(memberRef, {
        workspaceId: workspaceRef.id,
        userId,
        role: 'owner',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      // Handle profile creation/linking
      if (workspaceData.useExistingProfile && workspaceData.existingProfileId) {
        // When using existing profile, we don't create a new profile document
        // The existing profile remains linked to its original workspace
        // Multiple workspaces can reference the same profile
        console.log('Using existing profile:', workspaceData.existingProfileId)
      }
      else {
        // Create new profile for this workspace
        const profileRef = doc(firestore, 'profiles', `${userId}_${workspaceRef.id}`)
        await setDoc(profileRef, {
          userId,
          workspaceId: workspaceRef.id,
          displayName: workspaceData.newProfileData?.displayName || state.value.user.username,
          bio: workspaceData.newProfileData?.bio || null,
          avatarUrl: null,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          deletedAt: null,
        })
      }

      // Add workspace to state
      const newWorkspace: Workspace = {
        id: workspaceRef.id,
        ...workspaceDoc,
        role: 'owner',
      }

      // Update workspaces list
      state.value.workspaces = [...state.value.workspaces, newWorkspace]
      workspacesCookie.value = state.value.workspaces

      // Switch to the new workspace
      await setCurrentWorkspace(workspaceRef.id)

      showSuccess('Workspace created successfully!')
      return { success: true, workspaceId: workspaceRef.id }
    }
    catch (error: any) {
      console.error('Create workspace error:', error)
      showError(error.message || 'Failed to create workspace')
      throw error
    }
  }

  return {
    // State
    user: computed(() => state.value.user),
    currentWorkspace: computed(() => state.value.currentWorkspace),
    currentProfile: computed(() => state.value.currentProfile),
    workspaces: computed(() => state.value.workspaces),
    isAuthenticated: computed(() => state.value.isAuthenticated),

    // Getters
    getCurrentWorkspaceId,
    getCurrentProfileId,
    getCurrentWorkspaceRole,
    isWorkspaceOwner,
    isWorkspaceAdmin,

    // Actions
    login,
    signup,
    logout,
    resetPassword,
    loginWithGoogle,
    switchWorkspace,
    setCurrentWorkspace,
    createWorkspace,
    getUserProfiles,

    // Additional
    accountTypes,
    accountTools,
    makeUUID,
  }
}

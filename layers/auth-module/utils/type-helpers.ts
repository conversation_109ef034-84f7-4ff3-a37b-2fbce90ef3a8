/**
 * Type helper utilities for handling null/undefined conversions
 */

/**
 * Converts null values to undefined for optional properties
 * This is useful when working with Firestore data that uses null
 * but TypeScript interfaces expect undefined for optional properties
 */
export function nullToUndefined<T>(value: T | null): T | undefined {
  return value === null ? undefined : value
}

/**
 * Converts an object's null properties to undefined
 * Useful for converting Firestore documents to TypeScript interfaces
 */
export function convertNullsToUndefined<T extends Record<string, any>>(obj: T): T {
  const result = { ...obj }
  
  for (const key in result) {
    if (result[key] === null) {
      result[key] = undefined as any
    }
  }
  
  return result
}

/**
 * Type guard to check if a value is defined (not null or undefined)
 */
export function isDefined<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined
}

/**
 * Safely access nested properties, returning undefined instead of throwing
 */
export function safeGet<T, K extends keyof T>(
  obj: T | null | undefined,
  key: K
): T[K] | undefined {
  return obj?.[key]
}
/**
 * Firestore Helper Utilities
 *
 * This file contains utilities to help handle WebChannelConnection RPC errors
 * and other Firestore connection issues.
 */

/**
 * Enhanced error handler for Firestore operations
 * Specifically handles WebChannelConnection RPC errors
 */
export function handleFirestoreError(error: any, operation: string = 'Firestore operation') {
  console.error(`[Firestore] ${operation} failed:`, error)

  // Check for specific WebChannelConnection errors
  if (error.message?.includes('WebChannelConnection') || error.message?.includes('RPC')) {
    console.warn('[Firestore] WebChannelConnection RPC error detected')
    console.warn('[Firestore] This is usually a network connectivity issue')
    console.warn('[Firestore] Try the following:')
    console.warn('  1. Check your internet connection')
    console.warn('  2. Restart Firebase emulators if in development')
    console.warn('  3. Check Firebase console for service status')

    return {
      isNetworkError: true,
      shouldRetry: true,
      userMessage: 'Connection issue detected. Please check your internet connection and try again.',
    }
  }

  // Check for permission errors
  if (error.code === 'permission-denied') {
    return {
      isNetworkError: false,
      shouldRetry: false,
      userMessage: 'Permission denied. Please check your authentication status.',
    }
  }

  // Check for quota exceeded
  if (error.code === 'resource-exhausted') {
    return {
      isNetworkError: false,
      shouldRetry: true,
      userMessage: 'Service temporarily unavailable. Please try again in a moment.',
    }
  }

  // Generic error
  return {
    isNetworkError: false,
    shouldRetry: true,
    userMessage: 'An error occurred. Please try again.',
  }
}

/**
 * Validates Firestore connection before operations
 */
export async function validateFirestoreConnection(firestore: any): Promise<boolean> {
  try {
    // Try a simple read operation to test connection
    const testDoc = firestore.doc('__connection_test__/test')
    await testDoc.get()
    return true
  }
  catch (error: any) {
    console.warn('[Firestore] Connection validation failed:', error.message)
    return false
  }
}

/**
 * Creates a circuit breaker pattern for Firestore operations
 */
export class FirestoreCircuitBreaker {
  private failureCount = 0
  private lastFailureTime = 0
  private readonly maxFailures = 3
  private readonly resetTimeout = 30000 // 30 seconds

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.isOpen()) {
      throw new Error('Circuit breaker is open. Service temporarily unavailable.')
    }

    try {
      const result = await operation()
      this.reset()
      return result
    }
    catch (error) {
      this.recordFailure()
      throw error
    }
  }

  private isOpen(): boolean {
    return this.failureCount >= this.maxFailures
      && (Date.now() - this.lastFailureTime) < this.resetTimeout
  }

  private recordFailure(): void {
    this.failureCount++
    this.lastFailureTime = Date.now()
  }

  private reset(): void {
    this.failureCount = 0
    this.lastFailureTime = 0
  }
}
